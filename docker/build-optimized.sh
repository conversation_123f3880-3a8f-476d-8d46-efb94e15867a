#!/bin/bash

# Optimized Docker Build Script for Maximum Caching
# This script builds Docker images with maximum cache efficiency

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1
CACHE_FROM_REGISTRY=${CACHE_FROM_REGISTRY:-false}
REGISTRY=${REGISTRY:-""}
PUSH_TO_REGISTRY=${PUSH_TO_REGISTRY:-false}

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker BuildKit is enabled
check_buildkit() {
    if [ "${DOCKER_BUILDKIT:-0}" != "1" ]; then
        print_warning "Docker BuildKit not enabled. Enabling for optimal caching..."
        export DOCKER_BUILDKIT=1
        export COMPOSE_DOCKER_CLI_BUILD=1
    fi
    print_success "Docker BuildKit enabled"
}

# Function to build with maximum caching
build_with_cache() {
    local dockerfile=$1
    local image_name=$2
    local context=${3:-"."}
    
    print_status "Building $image_name with optimized caching..."
    
    # Build arguments for cache optimization
    local build_args=(
        "--file" "$dockerfile"
        "--tag" "$image_name"
        "--target" "runtime"
        "--build-arg" "BUILDKIT_INLINE_CACHE=1"
    )
    
    # Add cache from registry if enabled
    if [ "$CACHE_FROM_REGISTRY" = "true" ] && [ -n "$REGISTRY" ]; then
        build_args+=(
            "--cache-from" "$REGISTRY/$image_name:cache"
            "--cache-from" "$REGISTRY/$image_name:latest"
        )
    fi
    
    # Add cache mount optimization
    build_args+=(
        "--build-arg" "UV_CACHE_DIR=/root/.cache/uv"
    )
    
    # Execute build
    if docker build "${build_args[@]}" "$context"; then
        print_success "Successfully built $image_name"
        
        # Push to registry if enabled
        if [ "$PUSH_TO_REGISTRY" = "true" ] && [ -n "$REGISTRY" ]; then
            print_status "Pushing $image_name to registry..."
            docker tag "$image_name" "$REGISTRY/$image_name:latest"
            docker tag "$image_name" "$REGISTRY/$image_name:cache"
            docker push "$REGISTRY/$image_name:latest"
            docker push "$REGISTRY/$image_name:cache"
            print_success "Pushed $image_name to registry"
        fi
        
        return 0
    else
        print_error "Failed to build $image_name"
        return 1
    fi
}

# Function to show cache statistics
show_cache_stats() {
    print_status "Docker cache statistics:"
    docker system df
    echo
    print_status "Build cache usage:"
    docker builder du
}

# Function to prune old cache (optional)
prune_cache() {
    local prune_older_than=${1:-"24h"}
    print_warning "Pruning build cache older than $prune_older_than..."
    docker builder prune --filter "until=$prune_older_than" --force
    print_success "Cache pruned"
}

# Main build function
main() {
    print_status "Starting optimized Docker build process..."
    
    # Check prerequisites
    check_buildkit
    
    # Show initial cache stats
    show_cache_stats
    
    # Build base image first (most cacheable)
    print_status "Building base image..."
    if build_with_cache "docker/Dockerfile.base" "base:latest"; then
        print_success "Base image built successfully"
    else
        print_error "Failed to build base image"
        exit 1
    fi
    
    # Build service-specific images
    local services=("socket_v2" "auth" "management")
    
    for service in "${services[@]}"; do
        if [ -f "docker/Dockerfile.$service" ]; then
            print_status "Building $service service..."
            if build_with_cache "docker/Dockerfile.$service" "$service:latest"; then
                print_success "$service service built successfully"
            else
                print_warning "Failed to build $service service (continuing...)"
            fi
        else
            print_warning "Dockerfile for $service not found, skipping..."
        fi
    done
    
    # Show final cache stats
    echo
    print_status "Build completed. Final cache statistics:"
    show_cache_stats
    
    # Optional cache cleanup
    if [ "${PRUNE_CACHE:-false}" = "true" ]; then
        prune_cache "${PRUNE_OLDER_THAN:-24h}"
    fi
    
    print_success "All builds completed successfully!"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Optimized Docker build script with maximum caching efficiency.

OPTIONS:
    -h, --help              Show this help message
    -r, --registry REGISTRY Set registry for cache (e.g., ghcr.io/user/repo)
    -p, --push              Push images to registry
    -c, --cache-from        Use registry cache
    --prune                 Prune old cache after build
    --prune-older-than TIME Prune cache older than TIME (default: 24h)
    --stats-only            Show cache statistics only

ENVIRONMENT VARIABLES:
    DOCKER_BUILDKIT         Enable BuildKit (default: 1)
    REGISTRY               Registry URL for caching
    CACHE_FROM_REGISTRY    Use registry for cache (true/false)
    PUSH_TO_REGISTRY       Push to registry (true/false)
    PRUNE_CACHE           Prune cache after build (true/false)
    PRUNE_OLDER_THAN      Cache prune age (default: 24h)

EXAMPLES:
    # Basic build with local caching
    $0
    
    # Build with registry caching
    $0 --registry ghcr.io/user/repo --cache-from --push
    
    # Build with cache cleanup
    $0 --prune --prune-older-than 12h
    
    # Show cache stats only
    $0 --stats-only

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -r|--registry)
            REGISTRY="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -c|--cache-from)
            CACHE_FROM_REGISTRY=true
            shift
            ;;
        --prune)
            PRUNE_CACHE=true
            shift
            ;;
        --prune-older-than)
            PRUNE_OLDER_THAN="$2"
            shift 2
            ;;
        --stats-only)
            show_cache_stats
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Run main function
main
