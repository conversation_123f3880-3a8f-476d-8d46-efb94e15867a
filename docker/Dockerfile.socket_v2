# Optimized Dockerfile for Socket Service V2
FROM base:latest

# Set service-specific environment variables for V2 Optimized
ENV SERVICE_NAME=socket_service_v2 \
    SERVICE_VERSION=v2_optimized \
    UVICORN_HOST=0.0.0.0 \
    UVICORN_PORT=8002 \
    UVICORN_WORKERS=1 \
    UVICORN_LOOP=uvloop \
    UVICORN_HTTP=httptools

# Expose port for Socket Service V2
EXPOSE 8002

# Optimized health check for V2
HEALTHCHECK --interval=30s --timeout=10s --start-period=45s --retries=3 \
    CMD curl --fail --silent --max-time 5 http://localhost:8002/health || exit 1

# Optimized command with performance settings
CMD ["python", "-m", "uvicorn", \
     "app.v2.api.socket_service_v2:app", \
     "--host", "0.0.0.0", \
     "--port", "8002", \
     "--loop", "uvloop", \
     "--http", "httptools", \
     "--access-log", \
     "--log-level", "info"]
