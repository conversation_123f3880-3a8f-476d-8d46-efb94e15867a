"""
Async Task Processor for Audio Processing

Handles parallel processing of audio tasks with full async/await support.
Replaces the synchronous queue worker with efficient parallel processing.
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.task_utils import process_audio_with_prompt_maker, save_task_set_and_items
from app.shared.socketio.status_constants import EventNames

logger = setup_new_logging(__name__)


class AsyncTaskProcessor:
    """
    Async task processor for parallel audio processing.
    
    Features:
    - Full async/await processing
    - Parallel task execution
    - Automatic error handling
    - Socket.IO integration
    - Performance monitoring
    """

    def __init__(self, socketio_server=None):
        """
        Initialize async task processor.
        
        Args:
            socketio_server: Socket.IO server for real-time updates
        """
        self.socketio_server = socketio_server
        self.processing_tasks: Dict[str, asyncio.Task] = {}
        
        # Performance metrics
        self.total_processed = 0
        self.total_errors = 0
        self.processing_times = []

    async def process_audio_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single audio task with full parallelization.
        
        Args:
            task_data: Task information including user_id, session_id, input_content
            
        Returns:
            Processing result
        """
        task_id = task_data.get("task_id")
        user_id = task_data.get("user_id")
        session_id = task_data.get("session_id")
        socket_sid = task_data.get("socket_sid")
        
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Starting parallel audio processing for task {task_id}")
            
            # Send processing started notification
            if self.socketio_server and socket_sid:
                await self._notify_processing_started(socket_sid, task_id, session_id)
            
            # Get user context (this should be async)
            current_user = await self._get_user_context(user_id, task_data.get("tenant_id"))
            if not current_user:
                raise ValueError(f"User not found: {user_id}")
            
            # Get audio data from MinIO (async)
            audio_bytes = await self._fetch_audio_async(current_user, task_data["input_content"])
            
            # Process audio with prompt_maker (already async)
            num_tasks = task_data.get("num_tasks", 4)
            tasks_data = await process_audio_with_prompt_maker(
                current_user,
                audio_bytes,
                num_tasks=num_tasks
            )
            
            # Save tasks to database (async)
            if tasks_data.get("tasks"):
                save_result = await save_task_set_and_items(
                    current_user, 
                    session_id, 
                    tasks_data, 
                    None,  # task_set_id 
                    None,  # audio_storage_info
                    self.socketio_server.sio if self.socketio_server else None
                )
                
                # Send completion notification
                if self.socketio_server and socket_sid:
                    await self._notify_processing_complete(
                        socket_sid, 
                        task_id, 
                        session_id, 
                        save_result
                    )
                
                # Update metrics
                processing_time = time.time() - start_time
                self.total_processed += 1
                self.processing_times.append(processing_time)
                
                logger.info(f"✅ Completed audio processing for task {task_id} in {processing_time:.2f}s")
                
                return {
                    "status": "success",
                    "task_id": task_id,
                    "session_id": session_id,
                    "processing_time": processing_time,
                    "result": save_result
                }
            else:
                raise ValueError("No tasks generated from audio")
                
        except Exception as e:
            self.total_errors += 1
            processing_time = time.time() - start_time
            
            logger.error(f"❌ Error processing audio task {task_id}: {e}")
            
            # Send error notification
            if self.socketio_server and socket_sid:
                await self._notify_processing_error(socket_sid, task_id, session_id, str(e))
            
            return {
                "status": "error",
                "task_id": task_id,
                "session_id": session_id,
                "processing_time": processing_time,
                "error": str(e)
            }

    async def _get_user_context(self, user_id: str, tenant_id: str) -> Optional[Any]:
        """Get user context asynchronously."""
        try:
            # This should be implemented to fetch user context async
            # For now, using a placeholder
            from app.shared.database.user_tenant_db import UserTenantDB
            
            # TODO: Make this truly async
            return UserTenantDB(user_id=user_id, tenant_id=tenant_id)
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return None

    async def _fetch_audio_async(self, current_user: Any, input_content: Dict[str, Any]) -> bytes:
        """Fetch audio data asynchronously from MinIO."""
        try:
            object_path = input_content["object_path"]
            
            # Use async MinIO client if available
            if hasattr(current_user, 'async_minio'):
                return await current_user.async_minio.get_audio_bytes(object_path)
            else:
                # Fallback to sync operation in thread pool
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(
                    None, 
                    current_user.minio.get_audio_bytes, 
                    object_path
                )
                
        except Exception as e:
            logger.error(f"Error fetching audio: {e}")
            raise

    async def _notify_processing_started(self, socket_sid: str, task_id: str, session_id: str) -> None:
        """Send processing started notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_STARTED,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing started",
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending started notification: {e}")

    async def _notify_processing_complete(self, socket_sid: str, task_id: str, 
                                        session_id: str, result: Dict[str, Any]) -> None:
        """Send processing complete notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_COMPLETE,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing completed",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending completion notification: {e}")

    async def _notify_processing_error(self, socket_sid: str, task_id: str, 
                                     session_id: str, error: str) -> None:
        """Send processing error notification."""
        try:
            await self.socketio_server.sio.emit(
                EventNames.ToFrontend.TASK_GENERATION_ERROR,
                {
                    "task_id": task_id,
                    "session_id": session_id,
                    "message": "Audio processing failed",
                    "error": error,
                    "timestamp": datetime.now().isoformat()
                },
                room=socket_sid
            )
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")

    async def start_parallel_processing(self, max_concurrent: int = 10) -> None:
        """Start parallel processing with specified concurrency limit."""
        logger.info(f"🚀 Starting parallel task processing (max_concurrent: {max_concurrent})")
        # This would be implemented based on the queue manager integration

    async def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        avg_time = (
            sum(self.processing_times) / len(self.processing_times)
            if self.processing_times else 0
        )
        
        return {
            "total_processed": self.total_processed,
            "total_errors": self.total_errors,
            "active_tasks": len(self.processing_tasks),
            "average_processing_time": round(avg_time, 2),
            "success_rate": (
                (self.total_processed / (self.total_processed + self.total_errors)) * 100
                if (self.total_processed + self.total_errors) > 0 else 100
            )
        }
