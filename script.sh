#!/bin/bash

# Comprehensive script for Nepali App microservices
# Features:
# - Build and run services in development or production mode
# - Stop and clean up services
# - Selective rebuilding of services

# Set text colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Default values
MODE="prod"
ACTION="start"
REBUILD_BASE=false
SPECIFIC_SERVICE=""
NO_CACHE=false
REMOVE_IMAGES=false
PRUNE_NETWORKS=false
FORCE_MODE=false
SHOW_LOGS=false
VALID_SERVICES=("auth" "task" "task_management" "scoring" "media")

# Function to display usage information
show_usage() {
    echo -e "${BOLD}Usage:${NC} $0 [options]"
    echo
    echo -e "${BOLD}Options:${NC}"
    echo "  --dev                 Use development mode (reduced resources)"
    echo "  --prod                Use production mode (default, optimized for performance)"
    echo "  --stop                Stop all services"
    echo "  --restart             Restart all services"
    echo "  --status              Show service status"
    echo "  --build               Build services without starting them"
    echo "  --build-optimized     Use optimized build with maximum caching"
    echo "  --base                Rebuild only the base image"
    echo "  --service SERVICE     Rebuild a specific service (auth, socket_v2, management)"
    echo "  --no-cache            Force rebuild without using Docker cache"
    echo "  --remove-images       Remove all Nepali App Docker images when stopping"
    echo "  --prune-networks      Prune unused Docker networks when stopping"
    echo "  --force               Don't ask for confirmation when stopping"
    echo "  --logs                Show logs after starting services"
    echo "  --help                Show this help message"
    echo
    echo -e "${BOLD}Examples:${NC}"
    echo "  $0                    # Start in production mode"
    echo "  $0 --dev              # Start in development mode"
    echo "  $0 --stop             # Stop all services"
    echo "  $0 --restart          # Restart all services"
    echo "  $0 --service task     # Rebuild and start only the task service"
    echo "  $0 --build --no-cache # Build all services without cache but don't start them"
    echo
}

# Function to log messages
log() {
    local level=$1
    local message=$2
    local color=$NC

    case $level in
        "INFO") color=$GREEN ;;
        "WARN") color=$YELLOW ;;
        "ERROR") color=$RED ;;
        "DEBUG") color=$BLUE ;;
    esac

    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] ${color}${level}${NC}: ${message}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check dependencies
check_dependencies() {
    log "INFO" "Checking dependencies..."

    # Check for Docker
    if ! command_exists docker; then
        log "ERROR" "Docker is not installed. Please install Docker first."
        exit 1
    fi

    # Check for Docker Compose
    if ! command_exists docker; then
        docker compose version > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            log "ERROR" "Docker Compose plugin is not installed. Please install Docker Compose first."
            exit 1
        fi
    fi

    log "INFO" "All dependencies are installed."
}

# Function to set mode (development or production)
set_mode() {
    local mode=$1

    if [ "$mode" = "dev" ]; then
        log "INFO" "Setting development mode..."

        # Update .env file
        if [ -f ".env" ]; then
            sed -i 's/MODE=.*/MODE=development/' .env
        else
            echo "MODE=development" > .env
        fi

        COMPOSE_FILES="-f compose.yml -f compose.override.yml"
        log "INFO" "Development mode set. Using reduced resource settings."
    else
        log "INFO" "Setting production mode..."

        # Update .env file
        if [ -f ".env" ]; then
            sed -i 's/MODE=.*/MODE=production/' .env
        else
            echo "MODE=production" > .env
        fi

        COMPOSE_FILES="-f compose.yml"
        log "INFO" "Production mode set. Using optimized settings for performance."
    fi
}

# Function to build the base image
build_base_image() {
    log "INFO" "Building base image..."

    local cache_option=""
    if [ "$NO_CACHE" = true ]; then
        cache_option="--no-cache"
        log "INFO" "Building without cache (--no-cache)"
    fi

    # Build the base image
    docker compose $COMPOSE_FILES build $cache_option base

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to build base image."
        exit 1
    fi

    # Run the base image to ensure it's properly built
    docker compose $COMPOSE_FILES --profile build up base

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to verify base image."
        exit 1
    fi

    log "INFO" "Base image built successfully."
}

# Function to build a specific service
build_service() {
    local service=$1

    # Validate service name
    if [[ ! " ${VALID_SERVICES[@]} " =~ " ${service} " ]]; then
        log "ERROR" "Invalid service name: $service"
        log "INFO" "Valid services are: ${VALID_SERVICES[*]}"
        exit 1
    fi

    log "INFO" "Building service: $service"

    local cache_option=""
    if [ "$NO_CACHE" = true ]; then
        cache_option="--no-cache"
        log "INFO" "Building without cache (--no-cache)"
    fi

    # Build the specific service
    docker compose $COMPOSE_FILES build $cache_option $service

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to build service: $service"
        exit 1
    fi

    log "INFO" "Service $service built successfully."
}

# Function to build all services with optimization
build_all_services() {
    log "INFO" "Building all services with optimized caching..."

    # Check if optimized build script exists
    if [ -f "docker/build-optimized.sh" ]; then
        log "INFO" "Using optimized build script for maximum caching efficiency..."

        local build_args=""
        if [ "$NO_CACHE" = true ]; then
            build_args="--no-cache"
            log "INFO" "Building without cache (--no-cache)"
        fi

        # Use optimized build script
        if ./docker/build-optimized.sh $build_args; then
            log "INFO" "Optimized build completed successfully."
        else
            log "WARNING" "Optimized build failed, falling back to standard build..."
            build_all_services_fallback
        fi
    else
        log "INFO" "Optimized build script not found, using standard build..."
        build_all_services_fallback
    fi
}

# Fallback function for standard build
build_all_services_fallback() {
    log "INFO" "Building all services (standard method)..."

    local cache_option=""
    if [ "$NO_CACHE" = true ]; then
        cache_option="--no-cache"
        log "INFO" "Building without cache (--no-cache)"
    fi

    # Create required directories if they don't exist
    mkdir -p traefik/config traefik/certs

    # Enable BuildKit for better caching
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    # Build all services
    docker compose $COMPOSE_FILES build $cache_option

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to build services."
        exit 1
    fi

    log "INFO" "All services built successfully."
}

# Function to start all services
start_all_services() {
    log "INFO" "Starting all services..."

    # Start Redis and Traefik first to ensure they're ready
    docker compose $COMPOSE_FILES up -d redis traefik

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to start Redis and Traefik."
        exit 1
    fi

    # Wait for services to be ready
    log "INFO" "Waiting for Redis to be ready..."
    sleep 5

    # Start the rest of the services
    docker compose $COMPOSE_FILES up -d

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to start services."
        exit 1
    fi

    log "INFO" "All services started successfully."

    # Show service status
    log "INFO" "Current service status:"
    docker compose $COMPOSE_FILES ps

    # Show logs if requested
    if [ "$SHOW_LOGS" = true ]; then
        log "INFO" "Showing logs (press Ctrl+C to exit):"
        docker compose $COMPOSE_FILES logs -f
    fi
}

# Function to start a specific service
start_service() {
    local service=$1

    # Validate service name
    if [[ ! " ${VALID_SERVICES[@]} " =~ " ${service} " ]]; then
        log "ERROR" "Invalid service name: $service"
        log "INFO" "Valid services are: ${VALID_SERVICES[*]}"
        exit 1
    fi

    log "INFO" "Starting service: $service"

    # Ensure Redis and Traefik are running
    docker compose $COMPOSE_FILES up -d redis traefik

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to start Redis and Traefik."
        exit 1
    fi

    # Start the specific service
    docker compose $COMPOSE_FILES up -d $service

    if [ $? -ne 0 ]; then
        log "ERROR" "Failed to start service: $service"
        exit 1
    fi

    log "INFO" "Service $service started successfully."
}

# Function to stop all services
stop_services() {
    log "INFO" "Stopping and removing all Nepali App containers..."

    # Stop and remove main application containers
    log "INFO" "Stopping main application containers..."
    docker compose $COMPOSE_FILES down

    # Find and stop any remaining containers with the nepali_app prefix or docker prefix
    log "INFO" "Checking for any remaining Nepali App containers..."
    REMAINING_CONTAINERS=$(docker ps -a --filter "name=nepali_app" --format "{{.Names}}")
    DOCKER_CONTAINERS=$(docker ps -a --filter "name=docker" --format "{{.Names}}")

    if [ -n "$REMAINING_CONTAINERS" ] || [ -n "$DOCKER_CONTAINERS" ]; then
        log "WARN" "Found remaining containers:"

        if [ -n "$REMAINING_CONTAINERS" ]; then
            echo "$REMAINING_CONTAINERS"
        fi

        if [ -n "$DOCKER_CONTAINERS" ]; then
            echo "$DOCKER_CONTAINERS"
        fi

        log "INFO" "Stopping remaining containers..."
        docker stop $(docker ps -a --filter "name=nepali_app" -q) 2>/dev/null || true
        docker stop $(docker ps -a --filter "name=docker" -q) 2>/dev/null || true

        log "INFO" "Removing remaining containers..."
        docker rm $(docker ps -a --filter "name=nepali_app" -q) 2>/dev/null || true
        docker rm $(docker ps -a --filter "name=docker" -q) 2>/dev/null || true
    else
        log "INFO" "No remaining containers found."
    fi

    # Check if any containers are still running
    STILL_RUNNING=$(docker ps --filter "name=nepali_app" -q)
    if [ -n "$STILL_RUNNING" ]; then
        log "ERROR" "Warning: Some containers are still running:"
        docker ps --filter "name=nepali_app" --format "table {{.Names}}\t{{.Status}}"
    else
        log "INFO" "All Nepali App containers have been stopped and removed."
    fi

    # Handle image removal
    if [ "$REMOVE_IMAGES" = false ] && [ "$FORCE_MODE" = false ]; then
        echo -e "${YELLOW}Do you want to remove all Nepali App Docker images? (y/n)${NC}"
        read -r RESPONSE
        if [[ "$RESPONSE" =~ ^[Yy]$ ]]; then
            REMOVE_IMAGES=true
        fi
    fi

    if [ "$REMOVE_IMAGES" = true ]; then
        log "INFO" "Removing Nepali App Docker images..."
        docker rmi $(docker images "nepali-app-*" -q) 2>/dev/null || true
        log "INFO" "Images removed."
    fi

    # Handle network pruning
    if [ "$PRUNE_NETWORKS" = false ] && [ "$FORCE_MODE" = false ]; then
        echo -e "${YELLOW}Do you want to prune unused Docker networks? (y/n)${NC}"
        read -r RESPONSE
        if [[ "$RESPONSE" =~ ^[Yy]$ ]]; then
            PRUNE_NETWORKS=true
        fi
    fi

    if [ "$PRUNE_NETWORKS" = true ]; then
        log "INFO" "Pruning unused networks..."
        docker network prune -f --filter "until=1h"
        log "INFO" "Networks pruned."
    fi

    log "INFO" "Cleanup complete!"
}

# Function to check service health
check_service_health() {
    log "INFO" "Checking service health..."
    log "INFO" "Waiting for services to be ready..."
    sleep 10

    docker compose $COMPOSE_FILES ps

    # Check if all services are running
    EXPECTED_SERVICES=("auth" "task" "task_management" "scoring" "media")
    FAILED_SERVICES=()

    for service in "${EXPECTED_SERVICES[@]}"; do
        RUNNING=$(docker compose $COMPOSE_FILES ps --services --filter "status=running" | grep -c "$service")
        if [ "$RUNNING" -eq 0 ]; then
            FAILED_SERVICES+=("$service")
        fi
    done

    # If any services failed to start, check their logs
    if [ ${#FAILED_SERVICES[@]} -gt 0 ]; then
        log "ERROR" "The following services failed to start:"
        for service in "${FAILED_SERVICES[@]}"; do
            log "ERROR" "- $service"
            log "WARN" "Logs for $service:"
            docker compose $COMPOSE_FILES logs --tail=50 "$service"
            echo ""
        done
        log "ERROR" "Please check the logs above for errors."
    else
        log "INFO" "All services are running."
    fi
}

# Function to show service status
show_status() {
    log "INFO" "Current service status:"
    docker compose $COMPOSE_FILES ps

    # Check current mode
    if [ -f ".env" ] && grep -q "MODE=production" .env; then
        echo -e "${GREEN}Current mode:${NC} ${BOLD}Production${NC}"
        echo "Using optimized settings for performance."
    elif [ -f ".env" ] && grep -q "MODE=development" .env; then
        echo -e "${YELLOW}Current mode:${NC} ${BOLD}Development${NC}"
        echo "Using reduced resource settings for local development."
    else
        echo -e "${BLUE}Current mode:${NC} ${BOLD}Unknown${NC}"
        echo "Mode not set or .env file not found."
    fi
}

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dev)
            MODE="dev"
            shift
            ;;
        --prod)
            MODE="prod"
            shift
            ;;
        --stop)
            ACTION="stop"
            shift
            ;;
        --restart)
            ACTION="restart"
            shift
            ;;
        --status)
            ACTION="status"
            shift
            ;;
        --build)
            ACTION="build"
            shift
            ;;
        --base)
            REBUILD_BASE=true
            shift
            ;;
        --service)
            if [ -z "$2" ] || [[ "$2" == --* ]]; then
                log "ERROR" "Missing service name after --service"
                show_usage
                exit 1
            fi
            SPECIFIC_SERVICE="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        --remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        --prune-networks)
            PRUNE_NETWORKS=true
            shift
            ;;
        --force)
            FORCE_MODE=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            log "ERROR" "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main function
main() {
    # Check dependencies
    check_dependencies

    # Set the mode (development or production)
    set_mode "$MODE"

    # Handle different actions
    case "$ACTION" in
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            if [ "$REBUILD_BASE" = true ]; then
                build_base_image
            fi
            if [ -n "$SPECIFIC_SERVICE" ]; then
                build_service "$SPECIFIC_SERVICE"
                start_service "$SPECIFIC_SERVICE"
            else
                build_all_services
                start_all_services
            fi
            check_service_health
            ;;
        "status")
            show_status
            ;;
        "build")
            if [ "$REBUILD_BASE" = true ]; then
                build_base_image
            elif [ -n "$SPECIFIC_SERVICE" ]; then
                build_service "$SPECIFIC_SERVICE"
            else
                build_base_image
                build_all_services
            fi
            ;;
        "start")
            if [ "$REBUILD_BASE" = true ]; then
                build_base_image
            fi

            if [ -n "$SPECIFIC_SERVICE" ]; then
                build_service "$SPECIFIC_SERVICE"
                start_service "$SPECIFIC_SERVICE"
            else
                build_base_image
                build_all_services
                start_all_services
            fi

            check_service_health

            # Print access information
            echo -e "\n${GREEN}You can now access the following services:${NC}"
            echo -e "- API Services: http://localhost:8204"
            echo -e "  - Auth Service: http://localhost:8204/v1/auth"
            echo -e "  - Task Service: http://localhost:8204/v1/tasks"
            echo -e "  - Task Management: http://localhost:8204/v1/task-management"
            echo -e "  - Scoring Service: http://localhost:8204/v1/scoring"
            echo -e "  - Media Service: http://localhost:8204/v1/media"
            echo -e "- Traefik Dashboard: http://localhost:8205"

            echo -e "\n${YELLOW}Useful commands:${NC}"
            echo -e "  $0 --status              # Check service status"
            echo -e "  $0 --logs                # View all logs"
            echo -e "  $0 --stop                # Stop all services"
            echo -e "  $0 --restart             # Restart all services"
            ;;
    esac
}

# Run the main function
main